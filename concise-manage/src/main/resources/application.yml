#服务配置
server:
  port: 6010
  max-http-header-size: 10240
  servlet:
    context-path: /api

#spring相关配置
spring:
  application:
    name: @artifactId@
  profiles:
    active: dev
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 100MB
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    locale: zh_CN
    serialization:
      # 格式化输出
      indent_output: false

#mybaits相关配置
mybatis-plus:
  mapper-locations: classpath*:com/concise/**/mapping/*.xml, classpath:/META-INF/modeler-mybatis-mappings/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    banner: false
    db-config:
      id-type: assign_id
      table-underline: true
    enable-sql-runner: true
  configuration-properties:
    prefix:
    #如果数据库为postgresql，则需要配置为blobType: BINARY
    blobType: BLOB
    #如果数据库为oracle或mssql，则需要配置为boolValue: 1
    boolValue: true

#libreoffice文档在线预览配置
# CentOS 下安装 libreoffice：
# 安装：yum -y install libreoffice
# Linux 中文字体乱码解决：
# 1、上传 C:\Windows\Fonts 下的字体到 /usr/share/fonts/windows 目录
# 2、执行命令： chmod 644 /usr/share/fonts/windows/* && fc-cache -fv
jodconverter:
  local:
    #暂时关闭预览，启动时会有点慢
    enabled: false
    #设置libreoffice主目录 linux地址如：/usr/lib64/libreoffice
    office-home: C:\Program Files\LibreOffice
    #开启多个libreoffice进程，每个端口对应一个进程
    port-numbers: 8100
    #libreoffice进程重启前的最大进程数
    max-tasks-per-process: 100

#验证码相关配置 去除日志打印
logging:
  level:
    com.anji: off
    com.concise.gen: info
#验证码相关配置
aj:
  captcha:
    cache-type: local #分布式部署需要 自己实现CaptchaCacheService 使用redis需要配置redis相关配置
    type: default #验证码类型 clickword 为点选   blockPuzzle 为滑块验证码   default 两种都实例化
    font-type: 宋体
    req-frequency-limit-enable: true #接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 2  # 验证失败2次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔,s
    req-get-minute-limit: 30 # get接口一分钟内请求数限制
    req-check-minute-limit: 60 # check接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify接口一分钟内请求数限制

#SSO单点登录配置
sso:
  enabled: true # 是否启用SSO功能
  default-token-expire-seconds: 300 # 默认令牌有效期（秒）
  max-token-expire-seconds: 1800 # 最大令牌有效期（秒）
  encryption-algorithm: SM4 # 加密算法
  key-length: 16 # 密钥长度
  base-secret-key: ADHFMUudFU1DHKHB # 基础密钥
  enable-timestamp-validation: true # 是否启用时间戳验证
  enable-ip-whitelist: false # 是否启用IP白名单验证
  enable-logging: true # 是否记录SSO日志
  ip-whitelist: # IP白名单（当enable-ip-whitelist为true时生效）
    - 127.0.0.1
    - ***********/24
  systems: # 支持的目标系统配置
    system1:
      name: 子系统1
      description: 第一个子系统
      url: http://localhost:8081
      enabled: true
      token-expire-seconds: 600 # 自定义令牌有效期
      callback-url: http://localhost:8081/sso/callback
    system2:
      name: 子系统2
      description: 第二个子系统
      url: http://localhost:8082
      enabled: true
      custom-secret-key: CustomKey123456 # 自定义密钥
      callback-url: http://localhost:8082/sso/callback
