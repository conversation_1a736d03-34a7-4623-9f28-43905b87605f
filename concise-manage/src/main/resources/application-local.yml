# Mysql数据库
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************************************
    username: root
    password: Q0SCATk7VRgyR4i
  redis:
    host: localhost
    port: 6379
    password:


auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: "risen_IXVyVScZYjmTBq4x3kalog"
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: "jc9soUvlh2e8rNHPoa1xNClunJyjeMjTo1UgsdknGRPJ8mqeSBKCGNadzNXDuHAi"
    # 认证中心授权地址（必填）
    authorize-url: "https://one.lpxxfw.cn:7200/auth"
    # 获取token的地址（必填）
    token-url: "https://************:7100/typt/public/user/getUserInfoByToken"
    # 撤销token的地址（必填）
    revoke-url: "https://one.lpxxfw.cn:7200/auth/oauth2/revoke"
    # 登出地址（必填）
    logout-url: "https://one.lpxxfw.cn:7200/auth/logout"
    # 回调地址（必填）- 必须与注册时的地址一致，使用实际域名
    redirect-uri: "http://************:8050/#/transfer"
    # 授权范围（可选）- 默认为AUTH
    scope: "AUTH"
