package com.concise.modular.screen.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.common.pojo.response.ResponseData;
import com.concise.modular.correctionobjectbasic.entity.CorrectionObjectBasic;
import com.concise.modular.correctionobjectbasic.service.ICorrectionObjectBasicService;
import com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.modular.medicalrecord.entity.MedicalRecord;
import com.concise.modular.medicalrecord.service.MedicalRecordService;
import com.concise.modular.screen.vo.HealthCodeVo;
import com.concise.modular.screen.vo.PersonVo;
import com.concise.modular.screen.vo.RefreshSecretVo;
import com.concise.modular.screen.vo.ScreenVo;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.entity.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.service.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.qcloud.cos.utils.Md5Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: luo
 * @Date: 2022-11-17 10:55
 */
@Slf4j
@Api(tags = "大屏接口")
@RestController
public class ScreenController {
    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;
    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService;

    @Resource
    private MedicalRecordService medicalRecordService;

    @Resource
    private ICorrectionObjectBasicService correctionObjectBasicService;

    public static final String LP = "DEPT0000000000000000000000910500";

    @ApiOperation(value = "大屏-矫正对象数量")
    @GetMapping("/screen/correctionObjectInformation/quantity")
    public ResponseData quantity(@RequestParam(defaultValue = LP) String orgId) {
        if (LP.equals(orgId)) {
            List<String> collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
            int count = correctionObjectInformationService.count(new QueryWrapper<CorrectionObjectInformation>().lambda().in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getZhuangtai, "200"));
            List<ScreenVo> screenVos = correctionObjectInformationService.personnelClassification(collect);
            PersonVo personVo = new PersonVo();
            personVo.setJzdx(count);
            if (CollectionUtil.isNotEmpty(screenVos)) {
                for (ScreenVo screenVo : screenVos) {
                    if ("缓刑".equals(screenVo.getName())) {
                        personVo.setHx(screenVo.getValue());
                    }
                    if ("假释".equals(screenVo.getName())) {
                        personVo.setJs(screenVo.getValue());
                    }
                    if ("管制".equals(screenVo.getName())) {
                        personVo.setGz(screenVo.getValue());
                    }
                    if ("暂予监外执行".equals(screenVo.getName())) {
                        personVo.setZyjw(screenVo.getValue());
                    }
                }
            }

            return ResponseData.success(personVo);
        }
        int count = correctionObjectInformationService.count(new QueryWrapper<CorrectionObjectInformation>().lambda().eq(CorrectionObjectInformation::getJzjg, orgId).eq(CorrectionObjectInformation::getZhuangtai, "200"));
        List<ScreenVo> screenVos = correctionObjectInformationService.personnelClassification(Collections.singletonList(orgId));
        PersonVo personVo = new PersonVo();
        personVo.setJzdx(count);
        if (CollectionUtil.isNotEmpty(screenVos)) {
            for (ScreenVo screenVo : screenVos) {
                if ("缓刑".equals(screenVo.getName())) {
                    personVo.setHx(screenVo.getValue());
                }
                if ("假释".equals(screenVo.getName())) {
                    personVo.setJs(screenVo.getValue());
                }
                if ("管制".equals(screenVo.getName())) {
                    personVo.setGz(screenVo.getValue());
                }
                if ("暂予监外执行".equals(screenVo.getName())) {
                    personVo.setZyjw(screenVo.getValue());
                }
            }
        }
        return ResponseData.success(personVo);
    }

    @ApiOperation(value = "大屏-矫正对象详情")
    @GetMapping("/screen/correctionObjectInformation/detail")
    public ResponseData correctionObjectInformationDetail(@RequestParam(defaultValue = LP) String orgId, @RequestParam(defaultValue = "假释") String type) {
        List<ScreenVo> screenVoList = new ArrayList<>();
        List<CorrectionObjectInformation> correctionObjectInformationList;
        if (LP.equals(orgId)) {
            List<String> collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
            correctionObjectInformationList = correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getJzlbName, type).eq(CorrectionObjectInformation::getZhuangtai, "200"));
        } else {
            correctionObjectInformationList = correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().eq(CorrectionObjectInformation::getJzjg, orgId).eq(CorrectionObjectInformation::getJzlbName, type).eq(CorrectionObjectInformation::getZhuangtai, "200"));
        }
        if (CollectionUtil.isNotEmpty(correctionObjectInformationList)) {
            for (CorrectionObjectInformation correctionObjectInformation : correctionObjectInformationList) {
                ScreenVo screenVo = new ScreenVo();
                screenVo.setName(correctionObjectInformation.getXm());
                screenVo.setJzjg(correctionObjectInformation.getJzjgName());
                screenVo.setCsrq(correctionObjectInformation.getCsrq());
                screenVo.setWhcdName(correctionObjectInformation.getWhcdName());
                screenVo.setXbName(correctionObjectInformation.getXbName());
                if (!"浙江省".equals(correctionObjectInformation.getHjszsName())) {
                    screenVo.setHj("外省");
                } else if ("杭州市".equals(correctionObjectInformation.getHjszdsName())) {
                    screenVo.setHj("本市");
                } else {
                    screenVo.setHj("外市");
                }
                screenVoList.add(screenVo);
            }
        }
        return ResponseData.success(screenVoList);
    }

    @ApiOperation(value = "大屏-人员类别")
    @GetMapping("/screen/personType")
    public ResponseData personType(@RequestParam(defaultValue = LP) String orgId, @RequestParam(defaultValue = "1") String type) {
        List<ScreenVo> screenVoList = new ArrayList<>();
        List<String> collect = new ArrayList<>();
        if (LP.equals(orgId)) {
            collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
        } else {
            collect.add(orgId);
        }
        //重点非重点
        if ("1".equals(type)) {
            int yes = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getZddx, "1"));
            int no = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getZddx, "0"));
            ScreenVo yesVo = new ScreenVo();
            ScreenVo noVo = new ScreenVo();
            yesVo.setName("重点");
            yesVo.setValue(yes);
            noVo.setName("非重点");
            noVo.setValue(no);
            screenVoList.add(yesVo);
            screenVoList.add(noVo);
        }
        //普管严管
        if ("2".equals(type)) {
            int yes = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getJzjb, "2"));
            int no = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getJzjb, "1"));
            ScreenVo yesVo = new ScreenVo();
            ScreenVo noVo = new ScreenVo();
            yesVo.setName("普管");
            yesVo.setValue(yes);
            noVo.setName("严管");
            noVo.setValue(no);
            screenVoList.add(yesVo);
            screenVoList.add(noVo);
        }
        //就业就学
        if ("3".equals(type)) {
            int one = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getJyjxqk, "1"));
//            int two = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getJyjxqk, "2"));
//            int four = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getJyjxqk, "4"));
            ScreenVo oneVo = new ScreenVo();
            ScreenVo twoVo = new ScreenVo();
            ScreenVo fourVo = new ScreenVo();
            oneVo.setName("就学");
            oneVo.setValue(one);
            twoVo.setName("就业");
            //就业查询
            int two = correctionObjectBasicService.count(new LambdaQueryWrapper<CorrectionObjectBasic>().eq(CorrectionObjectBasic::getDqjyqk, "就业").in(CorrectionObjectBasic::getJzjg, collect));
            twoVo.setValue(two);
            fourVo.setName("无业");
            int four = correctionObjectBasicService.count(new LambdaQueryWrapper<CorrectionObjectBasic>().eq(CorrectionObjectBasic::getDqjyqk, "无业").in(CorrectionObjectBasic::getJzjg, collect));
            fourVo.setValue(four);
            screenVoList.add(oneVo);
            screenVoList.add(twoVo);
            screenVoList.add(fourVo);
        }
        //本省外省
        if ("4".equals(type)) {
            int one = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getHjszds, "1046"));
            int two = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).ne(CorrectionObjectInformation::getHjszds, "1046").eq(CorrectionObjectInformation::getHjszs, "1045"));
            int four = correctionObjectInformationService.count(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).ne(CorrectionObjectInformation::getHjszs, "1045"));
            ScreenVo oneVo = new ScreenVo();
            ScreenVo twoVo = new ScreenVo();
            ScreenVo fourVo = new ScreenVo();
            oneVo.setName("本地");
            oneVo.setValue(one);
            twoVo.setName("市外");
            twoVo.setValue(two);
            fourVo.setName("省外");
            fourVo.setValue(four);
            screenVoList.add(oneVo);
            screenVoList.add(twoVo);
            screenVoList.add(fourVo);
        }
        return ResponseData.success(screenVoList);
    }


    /**
     * 无业人员列表
     *
     * @param orgId
     * @return
     */
    @ApiOperation(value = "大屏-无业人员列表")
    @GetMapping("/screen/personType/noWork")
    public ResponseData noWork(@RequestParam(defaultValue = LP) String orgId) {
        List<String> collect = new ArrayList<>();
        if (LP.equals(orgId)) {
            collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
        } else {
            collect.add(orgId);
        }
        List<CorrectionObjectBasic> screenVoList = correctionObjectBasicService.list(new QueryWrapper<CorrectionObjectBasic>().select("xm,jzjg,jzjgName").lambda().in(CorrectionObjectBasic::getJzjg, collect).eq(CorrectionObjectBasic::getDqjyqk, "无业").eq(CorrectionObjectBasic::getZhuangtai, "200"));
        return ResponseData.success(screenVoList);
    }

    @ApiOperation(value = "大屏-人员类别-详情")
    @GetMapping("/screen/personType/detail")
    public ResponseData personTypeDetail(@RequestParam(defaultValue = LP) String orgId, @RequestParam(defaultValue = "1") String type) {
        List<ScreenVo> screenVoList = new ArrayList<>();
        List<String> collect = new ArrayList<>();
        if (LP.equals(orgId)) {
            collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
        } else {
            collect.add(orgId);
        }
        //重点非重点
        if ("1".equals(type)) {
            List<CorrectionObjectInformation> correctionObjectInformationList = correctionObjectInformationService.list(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getZddx, "1"));
            if (CollectionUtil.isNotEmpty(correctionObjectInformationList)) {
                for (CorrectionObjectInformation correctionObjectInformation : correctionObjectInformationList) {
                    ScreenVo screenVo = new ScreenVo();
                    screenVo.setName(correctionObjectInformation.getXm());
                    screenVo.setJzjg(correctionObjectInformation.getJzjgName());
                    screenVo.setCsrq(correctionObjectInformation.getCsrq());
                    screenVo.setWhcdName(correctionObjectInformation.getWhcdName());
                    screenVo.setXbName(correctionObjectInformation.getXbName());
                    if (!"浙江省".equals(correctionObjectInformation.getHjszsName())) {
                        screenVo.setHj("外省");
                    } else if ("杭州市".equals(correctionObjectInformation.getHjszdsName())) {
                        screenVo.setHj("本市");
                    } else {
                        screenVo.setHj("外市");
                    }
                    screenVoList.add(screenVo);
                }
            }
        }
        //就业就学
        if ("3".equals(type)) {
//            List<CorrectionObjectInformation> correctionObjectInformationList = correctionObjectInformationService.list(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getJyjxqk, "4"));
            List<CorrectionObjectBasic> correctionObjectInformationList = correctionObjectBasicService.list(new LambdaQueryWrapper<CorrectionObjectBasic>().eq(CorrectionObjectBasic::getDqjyqk, "无业").in(CorrectionObjectBasic::getJzjg, collect));
            if (CollectionUtil.isNotEmpty(correctionObjectInformationList)) {
                for (CorrectionObjectBasic CorrectionObjectBasic : correctionObjectInformationList) {
                    ScreenVo screenVo = new ScreenVo();
                    screenVo.setName(CorrectionObjectBasic.getXm());
                    screenVo.setJzjg(CorrectionObjectBasic.getJzjgName());
                    screenVo.setCsrq(CorrectionObjectBasic.getCsrq());
                    screenVo.setWhcdName(CorrectionObjectBasic.getWhcdName());
                    screenVo.setXbName(CorrectionObjectBasic.getXbName());
                    CorrectionObjectInformation objectInformation = correctionObjectInformationService.getById(CorrectionObjectBasic.getId());
                    if (ObjectUtil.isNotEmpty(objectInformation)) {
                        if (!"浙江省".equals(objectInformation.getHjszsName())) {
                            screenVo.setHj("外省");
                        } else if ("杭州市".equals(objectInformation.getHjszdsName())) {
                            screenVo.setHj("本市");
                        } else {
                            screenVo.setHj("外市");
                        }
                    }
                    screenVoList.add(screenVo);
                }
            }
        }
        //本省外省
        if ("4".equals(type)) {
            List<CorrectionObjectInformation> correctionObjectInformationList = correctionObjectInformationService.list(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect).ne(CorrectionObjectInformation::getHjszs, "1045"));
            if (CollectionUtil.isNotEmpty(correctionObjectInformationList)) {
                for (CorrectionObjectInformation correctionObjectInformation : correctionObjectInformationList) {
                    ScreenVo screenVo = new ScreenVo();
                    screenVo.setName(correctionObjectInformation.getXm());
                    screenVo.setJzjg(correctionObjectInformation.getJzjgName());
                    screenVo.setCsrq(correctionObjectInformation.getCsrq());
                    screenVo.setWhcdName(correctionObjectInformation.getWhcdName());
                    screenVo.setXbName(correctionObjectInformation.getXbName());
                    if (!"浙江省".equals(correctionObjectInformation.getHjszsName())) {
                        screenVo.setHj("外省");
                    } else if ("杭州市".equals(correctionObjectInformation.getHjszdsName())) {
                        screenVo.setHj("本市");
                    } else {
                        screenVo.setHj("外市");
                    }
                    screenVoList.add(screenVo);
                }
            }
        }
        return ResponseData.success(screenVoList);
    }

    @ApiOperation(value = "大屏-心有临悉预警")
    @GetMapping("/screen/diaryWarning")
    public ResponseData diaryWarning(@RequestParam(defaultValue = LP) String orgId) {
        HttpRequest request = HttpRequest.get("http://10.249.5.34:3717/api/screen/mainScreenWarning?orgId=" + orgId);
        String body = request.execute().body();
        JSONObject jsonObject = JSON.parseObject(body);
        return ResponseData.success(jsonObject.get("data"));
    }

    @ApiOperation(value = "大屏-单点心有临悉获取token")
    @GetMapping("/screen/diaryToken")
    public ResponseData diaryToken(@RequestParam String username) {
        HttpRequest request = new HttpRequest("http://10.249.5.34:3717/api/educationManageLogin");
        request.method(Method.POST);
        Map<String, Object> map = new HashMap<>();
        map.put("appKey", "9cf7a024-b84a-4601-93f0-d4f14f2a936b");
        map.put("correctId", username);
        request.form(map);
        String body = request.execute().body();
        JSONObject jsonObject = JSON.parseObject(body);
        return ResponseData.success(jsonObject.get("data"));
    }

    @ApiOperation(value = "大屏-多夸场景数据-核酸")
    @GetMapping("/screen/multiQuartSceneData/nucleicAcid")
    public ResponseData nucleicAcid(@RequestParam(defaultValue = LP) String orgId) {
        String nucleic = stringRedisTemplate.opsForValue().get("Nucleic");
        if (StringUtils.isEmpty(nucleic)) {
            nucleic = getAllNucleic();
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        List<HealthCodeVo> healthCodeVos = JSONObject.parseArray(nucleic, HealthCodeVo.class);
        if (LP.equals(orgId)) {
            hashMap.put("total", healthCodeVos.size());
            hashMap.put("detail", healthCodeVos);
            return ResponseData.success(hashMap);
        } else {
            List<HealthCodeVo> codeVos = healthCodeVos.stream().filter(e -> orgId.equals(e.getJzjg())).collect(Collectors.toList());
            hashMap.put("total", codeVos.size());
            hashMap.put("detail", codeVos);
            return ResponseData.success(hashMap);
        }
    }

    @ApiOperation(value = "大屏-多夸场景数据-就医")
    @GetMapping("/screen/medical")
    public ResponseData medical(@RequestParam(defaultValue = LP) String orgId) {


        return ResponseData.success();
    }

    public String getAllNucleic() {
        String url = "http://10.32.141.28/epi-api/oapi/epinatmonitoredperson/findAll";
        String appKey = "sfj";
        String appSecret = "sfj@mq2022";
        int page = 1;
        int size = 100;
        long ts = DateUtil.current();
        String sign = DigestUtil.md5Hex(appKey + appSecret + ts);
        Map<String, Object> map = new HashMap<>();
        map.put("page", page);
        map.put("size", size);
        map.put("appKey", appKey);
        map.put("ts", ts);
        map.put("sign", sign);
        HttpRequest request = new HttpRequest(url);
        request.method(Method.POST);
        request.form(map);
        String body = request.execute().body();
        JSONObject jsonObject = JSON.parseObject(body);
        if ("成功".equals(jsonObject.get("message"))) {
            Object data = jsonObject.get("data");
            String s = JSON.toJSONString(data);
            JSONObject json = JSON.parseObject(s);
            Object total = json.get("total");
            Object list = json.get("list");
            List<HealthCodeVo> healthCodeVos = JSON.parseArray(list.toString(), HealthCodeVo.class);
            if (CollectionUtil.isNotEmpty(healthCodeVos)) {
                for (HealthCodeVo healthCodeVo : healthCodeVos) {
                    CorrectionObjectInformation information = correctionObjectInformationService.getOne(new QueryWrapper<CorrectionObjectInformation>().lambda().eq(CorrectionObjectInformation::getSfzh, healthCodeVo.getIdNum()));
                    if (ObjectUtil.isNotEmpty(information)) {
                        healthCodeVo.setJzjgName(information.getJzjgName());
                        healthCodeVo.setJzjg(information.getJzjg());
                    }
                    healthCodeVo.setIdNum(null);
                    healthCodeVo.setHealthCode(null);
                }
            }
            String jsonString = JSONObject.toJSONString(healthCodeVos);
            stringRedisTemplate.opsForValue().set("Nucleic", jsonString, 6, TimeUnit.HOURS);
            return jsonString;
        }
        return null;
    }

    @ApiOperation(value = "大屏-多夸场景数据-低保")
    @GetMapping("/screen/multiQuartSceneData/subsistenceAllowances")
    public ResponseData subsistenceAllowances(@RequestParam(defaultValue = LP) String orgId) {
        List<String> collect = new ArrayList<>();

        if (LP.equals(orgId)) {
            collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
        } else {
            collect.add(orgId);
        }
        List<CorrectionObjectInformation> list = correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getZhuangtai, "200"));
        List<CorrectionObjectInformation> correctionObjectInformationList = new ArrayList<>();
        for (CorrectionObjectInformation correctionObjectInformation : list) {
            if (ObjectUtil.isNotEmpty(correctionObjectInformation.getSfdb())) {
                CorrectionObjectInformation information = new CorrectionObjectInformation();
                information.setXm(correctionObjectInformation.getXm());
                information.setJzjgName(correctionObjectInformation.getJzjgName());
                correctionObjectInformationList.add(information);
            }
        }
        HashMap<String, Object> newMap = new HashMap<>();
        newMap.put("total", correctionObjectInformationList.size());
        newMap.put("detail", correctionObjectInformationList);
        return ResponseData.success(newMap);
    }

    @ApiOperation(value = "大屏-多夸场景数据-低保")
    @GetMapping("/screen/multiQuartSceneData/medicalRecord")
    public ResponseData medicalRecord(@RequestParam(defaultValue = LP) String orgId) {
        List<String> collect = new ArrayList<>();
        if (LP.equals(orgId)) {
            collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
        } else {
            collect.add(orgId);
        }
        List<MedicalRecord> medicalRecordList = medicalRecordService.getRecordList(collect);
        Map<String, Object> map = new HashMap<>();
        map.put("counts", medicalRecordList.size());
        map.put("details", medicalRecordList);
        return ResponseData.success(map);
    }

    @ApiOperation(value = "大屏-多夸场景数据-社保")
    @GetMapping("/screen/multiQuartSceneData/socialSecurity")
    public ResponseData socialSecurity(@RequestParam(defaultValue = LP) String orgId) {
        List<ScreenVo> screenVoList = zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService.socialSecurity(orgId);
        Map<String, Object> map = new HashMap<>();
        map.put("uninsuredTotal", screenVoList.size());
        map.put("details", screenVoList);
        return ResponseData.success(map);
    }

    public Map countSubsistenceAllowances(String orgId) {
        String subsistence = stringRedisTemplate.opsForValue().get("subsistence:" + orgId);
        if (StringUtils.isBlank(subsistence)) {
            List<String> collect = new ArrayList<>();
            if (LP.equals(orgId)) {
                collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
            } else {
                collect.add(orgId);
            }
            List<CorrectionObjectInformation> list = correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getZhuangtai, "200"));
            List<CorrectionObjectInformation> correctionObjectInformationList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(list)) {
                for (CorrectionObjectInformation correctionObjectInformation : list) {
                    String subsistenceAllowances = getSubsistenceAllowances(correctionObjectInformation.getSfzh());
                    JSONObject jsonObject = JSONObject.parseObject(subsistenceAllowances);
                    if (ObjectUtil.isNotNull(jsonObject.get("datas"))) {
                        CorrectionObjectInformation information = new CorrectionObjectInformation();
                        information.setXm(correctionObjectInformation.getXm());
                        information.setJzjgName(correctionObjectInformation.getJzjgName());
                        correctionObjectInformationList.add(information);
                    }
                }
            }
            HashMap<String, Object> newMap = new HashMap<>();
            newMap.put("total", correctionObjectInformationList.size());
            newMap.put("detail", correctionObjectInformationList);
            String string = JSONObject.toJSONString(newMap);
            stringRedisTemplate.opsForValue().set("subsistence:" + orgId, string, 48, TimeUnit.HOURS);
            return newMap;
        }
        Map parse = (Map) JSON.parse(subsistence);
        return parse;
    }

    private String getSubsistenceAllowances(String sfzh) {
        String ip = "http://*************/gateway/api/001003010/dataSharing/lowestRescueNewInfo.htm";
        String requestSecret = getKey();
        String ak = "A330113259126202201000001";
        String requestTime = String.valueOf(DateUtil.current());
        String sign = Md5Utils.md5Hex(ak + requestSecret + requestTime);
        String url = ip + "?appKey=" + ak + "&sign=" + sign + "&requestTime=" + requestTime + "&aHAP0015=" + sfzh;
        log.debug(url);
        HashMap<String, Object> map = new HashMap<>();
        map.put("appKey", ak);
        map.put("sign", sign);
        map.put("requestTime", requestTime);
        map.put("aHAP0015", sfzh);
        map.put("bHAX0114", DateUtil.thisYear());
        map.put("bHAX0115", DateUtil.thisMonth());
        HttpRequest request = new HttpRequest(ip);
        request.header("X-SECURITY-USER-ID", "lpqszjzzhsqjzzxxt");
        request.header("X-SECURITY-CHECK-TYPE", "ZLB");

        request.form(map);
        request.method(Method.GET);
        try {
            String body = request.execute().body();
            log.info(body);
            return body;
        } catch (Exception e) {
            return null;
        }
    }

    private String getKey() {
        //获取请求密钥
        String requestSecret = stringRedisTemplate.opsForValue().get("requestSecret");
        //如果请求密钥失效
        if (StringUtils.isBlank(requestSecret)) {
            //看刷新密钥有没有过期
            String refreshSecret = stringRedisTemplate.opsForValue().get("refreshSecret");
            //如果刷新密钥过期
            if (StringUtils.isBlank(refreshSecret)) {
                log.debug("刷新密钥过期，重新获取刷新密钥和请求密钥");
                return refreshAllSecret();
            } else {
                log.debug("刷新密钥没过期，请求密钥过期，重新获取请求密钥，刷新密钥：{}", refreshSecret);
                //如果刷新密钥没过期，则用刷新密钥去刷新请求密钥
                String ip = "http://*************/gateway/app/refreshTokenBySec.htm";
                String ak = "A330113259126202201000001";
                String requestTime = String.valueOf(DateUtil.current());
                String sign = Md5Utils.md5Hex(ak + refreshSecret + requestTime);
                String url = ip + "?appKey=" + ak + "&sign=" + sign + "&requestTime=" + requestTime;
                HttpRequest request = new HttpRequest(url);
                request.header("X-SECURITY-USER-ID", "lpqszjzzhsqjzzxxt");
                request.header("X-SECURITY-CHECK-TYPE", "ZLB");
                request.method(Method.GET);
                String body = request.execute().body();
                System.out.println(body);
                JSONObject jsonObject = JSONObject.parseObject(body);
                String code = (String) jsonObject.get("code");
                if ("00".equals(code)) {
                    String datas = jsonObject.getString("datas");
                    RefreshSecretVo refreshSecretVo = JSONObject.parseObject(datas, RefreshSecretVo.class);
                    long currentTimeMillis = System.currentTimeMillis();
                    long requestExpireDate = refreshSecretVo.getRequestSecretEndTime() - currentTimeMillis;
                    long refreshExpireDate = refreshSecretVo.getRefreshSecretEndTime() - currentTimeMillis;
                    String newRequestSecret = refreshSecretVo.getRequestSecret();
                    String newRefreshSecret = refreshSecretVo.getRefreshSecret();
                    stringRedisTemplate.opsForValue().set("requestSecret", newRequestSecret, requestExpireDate, TimeUnit.MILLISECONDS);
                    stringRedisTemplate.opsForValue().set("refreshSecret", newRefreshSecret, refreshExpireDate, TimeUnit.MILLISECONDS);
                    log.debug("请求密钥更新成功,刷新密钥：{}，请求密钥：{}", newRefreshSecret, newRequestSecret);
                    return newRequestSecret;
                } else {
                    return refreshAllSecret();
                }
            }
        }
        log.info("走缓存：{}", requestSecret);
        return requestSecret;
    }

    private String refreshAllSecret() {
        //重新获取刷新密钥和请求密钥
        String ip = "http://*************/gateway/app/refreshTokenByKey.htm";
        String sk = "b073d96202884b82b769a6075561daaa";
        String ak = "A330113259126202201000001";
        String requestTime = String.valueOf(DateUtil.current());
        String sign = Md5Utils.md5Hex(ak + sk + requestTime);
        String url = ip + "?appKey=" + ak + "&sign=" + sign + "&requestTime=" + requestTime;
        HttpRequest request = new HttpRequest(url);
        request.header("X-SECURITY-USER-ID", "lpqszjzzhsqjzzxxt");
        request.header("X-SECURITY-CHECK-TYPE", "ZLB");
        request.method(Method.GET);
        String body = request.execute().body();
        System.out.println(body);
        JSONObject jsonObject = JSONObject.parseObject(body);
        String code = (String) jsonObject.get("code");
        if ("00".equals(code)) {
            String datas = jsonObject.getString("datas");
            RefreshSecretVo refreshSecretVo = JSONObject.parseObject(datas, RefreshSecretVo.class);
            long currentTimeMillis = System.currentTimeMillis();
            long requestExpireDate = refreshSecretVo.getRequestSecretEndTime() - currentTimeMillis;
            long refreshExpireDate = refreshSecretVo.getRefreshSecretEndTime() - currentTimeMillis;
            String newRequestSecret = refreshSecretVo.getRequestSecret();
            String newRefreshSecret = refreshSecretVo.getRefreshSecret();
            stringRedisTemplate.opsForValue().set("requestSecret", newRequestSecret, requestExpireDate, TimeUnit.MILLISECONDS);
            stringRedisTemplate.opsForValue().set("refreshSecret", newRefreshSecret, refreshExpireDate, TimeUnit.MILLISECONDS);
            log.debug("请求密钥和刷新密钥更新成功,刷新密钥：{}，请求密钥：{}", newRefreshSecret, newRequestSecret);
            return newRequestSecret;
        }
        return null;
    }

    @ApiOperation(value = "大屏-信息化")
    @GetMapping("/screen/ccgf/all")
    public ResponseData ccgfAll(@RequestParam(defaultValue = LP) String orgId) {
        HttpRequest request = HttpRequest.get("http://59.202.53.111:8082/ccgf/openApi/bigScreen/figureDriveScreen?deptId=" + orgId);
        String body = request.execute().body();
        JSONObject jsonObject = JSON.parseObject(body);
        return ResponseData.success(jsonObject.get("result"));
    }

    @ApiOperation(value = "大屏-信息化详情")
    @GetMapping("/screen/ccgf/detail")
    public ResponseData ccgfDetail(@RequestParam(defaultValue = LP) String orgId, @RequestParam(defaultValue = "1") String type, @RequestParam(defaultValue = "1") Integer pageNo, @RequestParam(defaultValue = "10") Integer pageSize) {
        HttpRequest request = HttpRequest.get("http://59.202.53.111:8082/ccgf/openApi/bigScreen/qwhcList?deptId=" + orgId + "&type=" + type + "&pageNo=" + pageNo + "&pageSize=" + pageSize);
        String body = request.execute().body();
        JSONObject jsonObject = JSON.parseObject(body);
        return ResponseData.success(jsonObject.get("result"));
    }

    @ApiOperation(value = "大屏-易帮矫key")
    @GetMapping("/screen/getYbjKey")
    public ResponseData ybjKey() {
        HttpRequest request = HttpRequest.get("http://admin.yibangjiao.com/adminpages/TbUser/publicKey.do");
        String body = request.execute().body();
        JSONObject jsonObject = JSON.parseObject(body);
        return ResponseData.success(jsonObject.get("publicKey"));
    }


//    @Scheduled(cron = "0 0 18 * * ?")
//    public void refreshSubsistenceAllowances() {
//        List<CorrectionObjectInformation> correctionObjectInformationList= correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().eq(CorrectionObjectInformation::getZhuangtai, "200"));
//        if (CollectionUtil.isNotEmpty(correctionObjectInformationList)) {
//            for (CorrectionObjectInformation correctionObjectInformation : correctionObjectInformationList) {
//                String subsistenceAllowances = getSubsistenceAllowances(correctionObjectInformation.getSfzh());
//                JSONObject jsonObject = JSONObject.parseObject(subsistenceAllowances);
//                if (ObjectUtil.isNotNull(jsonObject.get("datas"))) {
//                    correctionObjectInformation.setSfdb(jsonObject.getString("datas"));
//                }
//            }
//        }
//    }

}
