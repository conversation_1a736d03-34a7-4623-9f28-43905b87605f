package com.concise.modular.sso.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.modular.sso.entity.SsoTokenData;
import com.concise.modular.sso.entity.SsoUserInfo;
import com.concise.modular.sso.service.SsoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 单点登录控制器
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Slf4j
@Api(tags = "单点登录管理")
@RestController
@RequestMapping("/sso")
public class SsoController {

    @Resource
    private SsoService ssoService;

    /**
     * 生成单点登录参数
     *
     * @param targetSystem 目标系统标识
     * @return 加密的单点登录参数
     */
    @GetMapping("/generateToken")
    @ApiOperation(value = "生成单点登录令牌", notes = "为当前登录用户生成单点登录令牌，用于跳转到子系统")
    @BusinessLog(title = "生成单点登录令牌", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData generateSsoToken(@ApiParam("目标系统标识") @RequestParam(required = false) String targetSystem) {
        try {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();

            // 使用SSO服务生成令牌
            SsoTokenData tokenData = ssoService.generateSsoToken(sysLoginUser.getId(), targetSystem);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("ssoToken", tokenData.getSsoToken());
            result.put("timestamp", tokenData.getTimestamp());
            result.put("targetSystem", tokenData.getTargetSystem());
            result.put("expireSeconds", tokenData.getExpireSeconds());
            result.put("keyId", tokenData.getKeyId());

            log.info("为用户 {} 生成SSO令牌成功，目标系统: {}", sysLoginUser.getId(), targetSystem);

            return new SuccessResponseData(result);

        } catch (Exception e) {
            log.error("生成SSO令牌失败", e);
            return ResponseData.error("生成SSO令牌失败: " + e.getMessage());
        }
    }

    /**
     * 解析单点登录参数
     *
     * @param ssoToken 加密的SSO令牌
     * @return 解析后的用户信息
     */
    @PostMapping("/parseToken")
    @ApiOperation(value = "解析单点登录令牌", notes = "解析并验证单点登录令牌，返回用户信息")
    @BusinessLog(title = "解析单点登录令牌", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData parseSsoToken(@ApiParam("SSO令牌") @RequestParam String ssoToken) {
        try {
            // 使用SSO服务解析令牌
            SsoUserInfo userInfo = ssoService.parseSsoToken(ssoToken);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userInfo.getUserId());
            result.put("account", userInfo.getAccount());
            result.put("userName", userInfo.getUserName());
            result.put("nickName", userInfo.getNickName());
            result.put("orgId", userInfo.getOrgId());
            result.put("orgName", userInfo.getOrgName());
            result.put("orgList", userInfo.getOrgList());
            result.put("targetSystem", userInfo.getTargetSystem());
            result.put("loginTime", userInfo.getLoginTime());
            result.put("tokenTime", userInfo.getTokenTime());

            log.info("SSO令牌解析成功，用户: {}, 机构: {}", userInfo.getUserId(), userInfo.getOrgId());

            return new SuccessResponseData(result);

        } catch (Exception e) {
            log.error("解析SSO令牌失败", e);
            return ResponseData.error("解析SSO令牌失败: " + e.getMessage());
        }
    }

    /**
     * 验证单点登录令牌
     *
     * @param ssoToken 加密的SSO令牌
     * @return 验证结果
     */
    @PostMapping("/validateToken")
    @ApiOperation(value = "验证单点登录令牌", notes = "验证单点登录令牌是否有效")
    @BusinessLog(title = "验证单点登录令牌", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData validateSsoToken(@ApiParam("SSO令牌") @RequestParam String ssoToken) {
        try {
            boolean isValid = ssoService.validateSsoToken(ssoToken);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", isValid);
            result.put("validateTime", new Date());

            return new SuccessResponseData(result);

        } catch (Exception e) {
            log.error("验证SSO令牌失败", e);
            return ResponseData.error("验证SSO令牌失败: " + e.getMessage());
        }
    }

    /**
     * 生成单点登录跳转URL
     *
     * @param targetUrl 目标系统URL
     * @param targetSystem 目标系统标识
     * @return 完整的跳转URL
     */
    @GetMapping("/generateUrl")
    @ApiOperation(value = "生成单点登录跳转URL", notes = "生成包含SSO令牌的完整跳转URL")
    @BusinessLog(title = "生成单点登录跳转URL", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData generateSsoUrl(@ApiParam("目标系统URL") @RequestParam String targetUrl,
                                      @ApiParam("目标系统标识") @RequestParam(required = false) String targetSystem) {
        try {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
            
            // 使用SSO服务生成跳转URL
            String redirectUrl = ssoService.generateSsoUrl(targetUrl, sysLoginUser.getId(), targetSystem);
            
            Map<String, Object> result = new HashMap<>();
            result.put("redirectUrl", redirectUrl);
            result.put("targetSystem", targetSystem);
            result.put("generateTime", new Date());
            
            log.info("为用户 {} 生成SSO跳转URL成功，目标系统: {}", sysLoginUser.getId(), targetSystem);
            
            return new SuccessResponseData(result);
            
        } catch (Exception e) {
            log.error("生成SSO跳转URL失败", e);
            return ResponseData.error("生成SSO跳转URL失败: " + e.getMessage());
        }
    }

    /**
     * 刷新单点登录令牌
     *
     * @param oldToken 旧令牌
     * @return 新的SSO令牌
     */
    @PostMapping("/refreshToken")
    @ApiOperation(value = "刷新单点登录令牌", notes = "刷新即将过期的SSO令牌")
    @BusinessLog(title = "刷新单点登录令牌", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData refreshSsoToken(@ApiParam("旧令牌") @RequestParam String oldToken) {
        try {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
            
            // 使用SSO服务刷新令牌
            SsoTokenData tokenData = ssoService.refreshSsoToken(oldToken, sysLoginUser.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("ssoToken", tokenData.getSsoToken());
            result.put("timestamp", tokenData.getTimestamp());
            result.put("expireSeconds", tokenData.getExpireSeconds());
            result.put("keyId", tokenData.getKeyId());
            
            log.info("为用户 {} 刷新SSO令牌成功", sysLoginUser.getId());
            
            return new SuccessResponseData(result);
            
        } catch (Exception e) {
            log.error("刷新SSO令牌失败", e);
            return ResponseData.error("刷新SSO令牌失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户的所有机构列表
     *
     * @return 用户机构列表
     */
    @GetMapping("/listUserOrganizations")
    @ApiOperation(value = "获取用户机构列表", notes = "获取当前登录用户的所有机构列表")
    @BusinessLog(title = "获取用户机构列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData listUserOrganizations() {
        try {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
            
            // 获取用户所有机构列表
            List<Map<String, Object>> orgList = ssoService.getUserOrganizations(sysLoginUser.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("userId", sysLoginUser.getId());
            result.put("currentOrgId", sysLoginUser.getLoginEmpInfo() != null ? sysLoginUser.getLoginEmpInfo().getOrgId() : null);
            result.put("organizations", orgList);
            
            log.info("获取用户 {} 的机构列表成功", sysLoginUser.getId());
            
            return new SuccessResponseData(result);
            
        } catch (Exception e) {
            log.error("获取用户机构列表失败", e);
            return ResponseData.error("获取用户机构列表失败: " + e.getMessage());
        }
    }

    /**
     * 切换用户当前机构
     *
     * @param orgId 目标机构ID
     * @return 切换结果
     */
    @PostMapping("/switchOrganization")
    @ApiOperation(value = "切换用户机构", notes = "切换当前登录用户的所属机构")
    @BusinessLog(title = "切换用户机构", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData switchOrganization(@ApiParam("机构ID") @RequestParam String orgId) {
        try {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
            
            // 切换用户机构
            boolean success = ssoService.switchUserOrganization(sysLoginUser.getId(), Long.valueOf(orgId));
            
            if (!success) {
                return ResponseData.error("切换机构失败：用户无权限访问该机构");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("userId", sysLoginUser.getId());
            result.put("newOrgId", orgId);
            result.put("switchTime", new Date());
            
            log.info("用户 {} 成功切换到机构 {}", sysLoginUser.getId(), orgId);
            
            return new SuccessResponseData(result);
            
        } catch (Exception e) {
            log.error("切换用户机构失败", e);
            return ResponseData.error("切换用户机构失败: " + e.getMessage());
        }
    }

    /**
     * 获取SSO配置信息
     *
     * @return SSO配置信息
     */
    @GetMapping("/config")
    @ApiOperation(value = "获取SSO配置信息", notes = "获取单点登录相关配置信息")
    @BusinessLog(title = "获取SSO配置信息", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getSsoConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("tokenExpireSeconds", 300); // 令牌有效期
            config.put("maxTokenExpireSeconds", 1800); // 最大令牌有效期
            config.put("encryptionAlgorithm", "SM4"); // 加密算法
            config.put("keyLength", 16); // 密钥长度
            config.put("supportedSystems", new String[]{"system1", "system2", "system3"}); // 支持的目标系统
            
            return new SuccessResponseData(config);
            
        } catch (Exception e) {
            log.error("获取SSO配置信息失败", e);
            return ResponseData.error("获取SSO配置信息失败: " + e.getMessage());
        }
    }
}
