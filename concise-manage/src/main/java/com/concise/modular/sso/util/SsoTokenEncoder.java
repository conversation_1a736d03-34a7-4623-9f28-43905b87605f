package com.concise.modular.sso.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * SSO令牌编码器 - 生成URL安全的短令牌
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Slf4j
@Component
public class SsoTokenEncoder {

    /**
     * URL安全的Base64字符集（去除+/=等特殊字符）
     */
    private static final String URL_SAFE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";
    
    /**
     * 令牌长度
     */
    private static final int TOKEN_LENGTH = 32;

    /**
     * 生成URL安全的短令牌
     *
     * @param data 要编码的数据
     * @param secretKey 密钥
     * @return URL安全的短令牌
     */
    public String generateUrlSafeToken(Map<String, Object> data, String secretKey) {
        try {
            // 1. 将数据转换为JSON字符串
            String jsonData = JSONObject.toJSONString(data);
            
            // 2. 生成随机盐值
            String salt = RandomUtil.randomString(8);
            
            // 3. 组合数据：盐值 + JSON数据
            String combinedData = salt + ":" + jsonData;
            
            // 4. 使用密钥和盐值生成签名
            String signature = generateSignature(combinedData, secretKey);
            
            // 5. 组合最终数据：盐值 + 签名 + JSON数据
            String finalData = salt + ":" + signature + ":" + jsonData;
            
            // 6. Base64编码
            String base64Data = Base64.getEncoder().encodeToString(finalData.getBytes(StandardCharsets.UTF_8));
            
            // 7. 转换为URL安全格式
            String urlSafeToken = toUrlSafeString(base64Data);
            
            // 8. 截取到指定长度（如果太长）
            if (urlSafeToken.length() > TOKEN_LENGTH) {
                // 使用哈希确保唯一性
                String hash = DigestUtil.md5Hex(urlSafeToken);
                urlSafeToken = hash.substring(0, TOKEN_LENGTH);
            }
            
            log.debug("生成URL安全令牌成功，长度: {}", urlSafeToken.length());
            return urlSafeToken;
            
        } catch (Exception e) {
            log.error("生成URL安全令牌失败", e);
            throw new RuntimeException("生成URL安全令牌失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析URL安全的短令牌
     *
     * @param urlSafeToken URL安全的短令牌
     * @param secretKey 密钥
     * @return 解析后的数据
     */
    public Map<String, Object> parseUrlSafeToken(String urlSafeToken, String secretKey) {
        try {
            // 由于使用了哈希截取，无法直接解析
            // 这里需要从缓存或数据库中查找对应的完整令牌数据
            throw new UnsupportedOperationException("短令牌需要配合缓存系统使用，请使用完整令牌解析方法");
            
        } catch (Exception e) {
            log.error("解析URL安全令牌失败", e);
            throw new RuntimeException("解析URL安全令牌失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成完整的URL安全令牌（不截取长度）
     *
     * @param data 要编码的数据
     * @param secretKey 密钥
     * @return 完整的URL安全令牌
     */
    public String generateFullUrlSafeToken(Map<String, Object> data, String secretKey) {
        try {
            // 1. 将数据转换为JSON字符串
            String jsonData = JSONObject.toJSONString(data);
            
            // 2. 生成随机盐值
            String salt = RandomUtil.randomString(8);
            
            // 3. 组合数据：盐值 + JSON数据
            String combinedData = salt + ":" + jsonData;
            
            // 4. 使用密钥和盐值生成签名
            String signature = generateSignature(combinedData, secretKey);
            
            // 5. 组合最终数据：盐值 + 签名 + JSON数据
            String finalData = salt + ":" + signature + ":" + jsonData;
            
            // 6. Base64编码
            String base64Data = Base64.getEncoder().encodeToString(finalData.getBytes(StandardCharsets.UTF_8));
            
            // 7. 转换为URL安全格式
            String urlSafeToken = toUrlSafeString(base64Data);
            
            log.debug("生成完整URL安全令牌成功，长度: {}", urlSafeToken.length());
            return urlSafeToken;
            
        } catch (Exception e) {
            log.error("生成完整URL安全令牌失败", e);
            throw new RuntimeException("生成完整URL安全令牌失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析完整的URL安全令牌
     *
     * @param urlSafeToken 完整的URL安全令牌
     * @param secretKey 密钥
     * @return 解析后的数据
     */
    public Map<String, Object> parseFullUrlSafeToken(String urlSafeToken, String secretKey) {
        try {
            // 1. 从URL安全格式转换回Base64
            String base64Data = fromUrlSafeString(urlSafeToken);
            
            // 2. Base64解码
            String finalData = new String(Base64.getDecoder().decode(base64Data), StandardCharsets.UTF_8);
            
            // 3. 分割数据：盐值:签名:JSON数据
            String[] parts = finalData.split(":", 3);
            if (parts.length != 3) {
                throw new IllegalArgumentException("令牌格式错误");
            }
            
            String salt = parts[0];
            String signature = parts[1];
            String jsonData = parts[2];
            
            // 4. 验证签名
            String combinedData = salt + ":" + jsonData;
            String expectedSignature = generateSignature(combinedData, secretKey);
            if (!signature.equals(expectedSignature)) {
                throw new IllegalArgumentException("令牌签名验证失败");
            }
            
            // 5. 解析JSON数据
            @SuppressWarnings("unchecked")
            Map<String, Object> data = JSONObject.parseObject(jsonData, Map.class);
            
            log.debug("解析完整URL安全令牌成功");
            return data;
            
        } catch (Exception e) {
            log.error("解析完整URL安全令牌失败", e);
            throw new RuntimeException("解析完整URL安全令牌失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成签名
     *
     * @param data 数据
     * @param secretKey 密钥
     * @return 签名
     */
    private String generateSignature(String data, String secretKey) {
        String combined = secretKey + data;
        return DigestUtil.md5Hex(combined).substring(0, 16);
    }

    /**
     * 将Base64字符串转换为URL安全格式
     *
     * @param base64String Base64字符串
     * @return URL安全字符串
     */
    private String toUrlSafeString(String base64String) {
        return base64String.replace('+', '-')
                          .replace('/', '_')
                          .replace("=", "");
    }

    /**
     * 将URL安全格式转换回Base64字符串
     *
     * @param urlSafeString URL安全字符串
     * @return Base64字符串
     */
    private String fromUrlSafeString(String urlSafeString) {
        String base64String = urlSafeString.replace('-', '+')
                                          .replace('_', '/');
        
        // 补充填充字符
        int padding = 4 - (base64String.length() % 4);
        if (padding != 4) {
            StringBuilder sb = new StringBuilder(base64String);
            for (int i = 0; i < padding; i++) {
                sb.append("=");
            }
            base64String = sb.toString();
        }
        
        return base64String;
    }

    /**
     * 验证令牌格式是否为URL安全格式
     *
     * @param token 令牌
     * @return 是否为URL安全格式
     */
    public boolean isUrlSafeToken(String token) {
        if (token == null || token.isEmpty()) {
            return false;
        }
        
        // 检查是否只包含URL安全字符
        for (char c : token.toCharArray()) {
            if (URL_SAFE_CHARS.indexOf(c) == -1) {
                return false;
            }
        }
        
        return true;
    }
}
