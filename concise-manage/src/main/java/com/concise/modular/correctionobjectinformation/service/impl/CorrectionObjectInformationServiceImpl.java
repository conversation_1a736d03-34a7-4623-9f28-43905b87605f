package com.concise.modular.correctionobjectinformation.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.correctionobjectinformation.entity.CorrectionTerminate;
import com.concise.modular.correctionobjectinformation.enums.CorrectionObjectInformationExceptionEnum;
import com.concise.modular.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.modular.correctionobjectinformation.param.CorrectionObjectInformationParam;
import com.concise.modular.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.modular.screen.vo.ScreenVo;
import com.jiashi.diary.correctionobjectinformationdelete.entity.CorrectionObjectInformationDelete;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import lombok.extern.slf4j.Slf4j;

/**
 * 矫正对象信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2021-09-10 17:17:06
 */
@Slf4j
@DS(value = "dataCenter")
@Service
public class CorrectionObjectInformationServiceImpl extends ServiceImpl<CorrectionObjectInformationMapper, CorrectionObjectInformation> implements CorrectionObjectInformationService {
    @Resource
    private CorrectionObjectInformationMapper correctionObjectInformationMapper;

    @Override
    public PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam correctionObjectInformationParam) {
        QueryWrapper<CorrectionObjectInformation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionObjectInformationParam)) {

            // 根据社区矫正人员编号 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzrybh())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSqjzrybh, correctionObjectInformationParam.getSqjzrybh());
            }
            // 根据是否调查评估 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfdcpg())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfdcpg, correctionObjectInformationParam.getSfdcpg());
            }
            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzlb())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJzlb, correctionObjectInformationParam.getJzlb());
            }
            // 根据矫正类别中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzlbName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJzlbName, correctionObjectInformationParam.getJzlbName());
            }
            // 根据是否成年 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfcn())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfcn, correctionObjectInformationParam.getSfcn());
            }
            // 根据是否成年中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfcnName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfcnName, correctionObjectInformationParam.getSfcnName());
            }
            // 根据未成年 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getWcn())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getWcn, correctionObjectInformationParam.getWcn());
            }
            // 根据未成年中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getWcnName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getWcnName, correctionObjectInformationParam.getWcnName());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getXm, correctionObjectInformationParam.getXm());
            }
            // 根据曾用名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getCym())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getCym, correctionObjectInformationParam.getCym());
            }
            // 根据性别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXb())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getXb, correctionObjectInformationParam.getXb());
            }
            // 根据性别中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXbName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getXbName, correctionObjectInformationParam.getXbName());
            }
            // 根据民族 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getMz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getMz, correctionObjectInformationParam.getMz());
            }
            // 根据民族中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getMzName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getMzName, correctionObjectInformationParam.getMzName());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfzh())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfzh, correctionObjectInformationParam.getSfzh());
            }
            // 根据出生日期 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getCsrq())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getCsrq, correctionObjectInformationParam.getCsrq());
            }
            // 根据有无港澳台身份证 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYwgatsfz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYwgatsfz, correctionObjectInformationParam.getYwgatsfz());
            }
            // 根据港澳台身份证类型 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGatsfzlx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGatsfzlx, correctionObjectInformationParam.getGatsfzlx());
            }
            // 根据港澳台身份证类型中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGatsfzlxName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGatsfzlxName, correctionObjectInformationParam.getGatsfzlxName());
            }
            // 根据港澳台身份证号码 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGatsfzhm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGatsfzhm, correctionObjectInformationParam.getGatsfzhm());
            }
            // 根据有无护照 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYwhz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYwhz, correctionObjectInformationParam.getYwhz());
            }
            // 根据护照号码 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHzhm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHzhm, correctionObjectInformationParam.getHzhm());
            }
            // 根据护照保存状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHzbczt())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHzbczt, correctionObjectInformationParam.getHzbczt());
            }
            // 根据护照保存状态中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHzbcztName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHzbcztName, correctionObjectInformationParam.getHzbcztName());
            }
            // 根据有无港澳台通行证 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYwgattxz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYwgattxz, correctionObjectInformationParam.getYwgattxz());
            }
            // 根据港澳台通行证类型 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGattxzlx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGattxzlx, correctionObjectInformationParam.getGattxzlx());
            }
            // 根据港澳台通行证类型中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGattxzlxName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGattxzlxName, correctionObjectInformationParam.getGattxzlxName());
            }
            // 根据港澳台通行证号码 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGattxzhm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGattxzhm, correctionObjectInformationParam.getGattxzhm());
            }
            // 根据港澳台通行证保存状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGattxzbczt())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGattxzbczt, correctionObjectInformationParam.getGattxzbczt());
            }
            // 根据港澳台通行证保存状态中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGattxzbcztName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGattxzbcztName, correctionObjectInformationParam.getGattxzbcztName());
            }
            // 根据有无港澳居民往来内地通行证 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYwgajmwlndtxz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYwgajmwlndtxz, correctionObjectInformationParam.getYwgajmwlndtxz());
            }
            // 根据港澳居往来内地通行证号码 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGajmwlndtxz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGajmwlndtxz, correctionObjectInformationParam.getGajmwlndtxz());
            }
            // 根据港澳居民往来内地通行证保存状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGajmwlndtxzbczt())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGajmwlndtxzbczt, correctionObjectInformationParam.getGajmwlndtxzbczt());
            }
            // 根据港澳居民往来内地通行证保存状态中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGajmwlndtxzbcztName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGajmwlndtxzbcztName, correctionObjectInformationParam.getGajmwlndtxzbcztName());
            }
            // 根据有无台胞证 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYwtbz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYwtbz, correctionObjectInformationParam.getYwtbz());
            }
            // 根据台胞证号码 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getTbzhm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getTbzhm, correctionObjectInformationParam.getTbzhm());
            }
            // 根据台胞证保存状态 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getTbzbczt())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getTbzbczt, correctionObjectInformationParam.getTbzbczt());
            }
            // 根据台胞证保存状态中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getTbzbcztName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getTbzbcztName, correctionObjectInformationParam.getTbzbcztName());
            }
            // 根据暂予监外执行人员身体状况 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZyjwzxrystzk())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getZyjwzxrystzk, correctionObjectInformationParam.getZyjwzxrystzk());
            }
            // 根据暂予监外执行人员身体状况中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZyjwzxrystzkName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getZyjwzxrystzkName, correctionObjectInformationParam.getZyjwzxrystzkName());
            }
            // 根据最后就诊医院 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZhjzyy())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getZhjzyy, correctionObjectInformationParam.getZhjzyy());
            }
            // 根据是否有精神病 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfyjsb())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfyjsb, correctionObjectInformationParam.getSfyjsb());
            }
            // 根据鉴定机构 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJdjg())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJdjg, correctionObjectInformationParam.getJdjg());
            }
            // 根据是否有传染病 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfycrb())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfycrb, correctionObjectInformationParam.getSfycrb());
            }
            // 根据具体传染病 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJtcrb())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJtcrb, correctionObjectInformationParam.getJtcrb());
            }
            // 根据具体传染病中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJtcrbName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJtcrbName, correctionObjectInformationParam.getJtcrbName());
            }
            // 根据文化程度 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getWhcd())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getWhcd, correctionObjectInformationParam.getWhcd());
            }
            // 根据文化程度中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getWhcdName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getWhcdName, correctionObjectInformationParam.getWhcdName());
            }
            // 根据婚姻状况 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHyzk())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHyzk, correctionObjectInformationParam.getHyzk());
            }
            // 根据婚姻状况中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHyzkName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHyzkName, correctionObjectInformationParam.getHyzkName());
            }
            // 根据捕前职业 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getPqzy())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getPqzy, correctionObjectInformationParam.getPqzy());
            }
            // 根据捕前职业中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getPqzyName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getPqzyName, correctionObjectInformationParam.getPqzyName());
            }
            // 根据就业就学情况 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJyjxqk())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJyjxqk, correctionObjectInformationParam.getJyjxqk());
            }
            // 根据就业就学情况中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJyjxqkName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJyjxqkName, correctionObjectInformationParam.getJyjxqkName());
            }
            // 根据现政治面貌 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXzzmn())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getXzzmn, correctionObjectInformationParam.getXzzmn());
            }
            // 根据现政治面貌中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXzzmnName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getXzzmnName, correctionObjectInformationParam.getXzzmnName());
            }
            // 根据原政治面貌 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYzzmm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYzzmm, correctionObjectInformationParam.getYzzmm());
            }
            // 根据原政治面貌中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYzzmmName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYzzmmName, correctionObjectInformationParam.getYzzmmName());
            }
            // 根据原工作单位 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYgzdw())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYgzdw, correctionObjectInformationParam.getYgzdw());
            }
            // 根据现工作单位中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getXgzdwName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getXgzdwName, correctionObjectInformationParam.getXgzdwName());
            }
            // 根据单位联系电话 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getDwlxdh())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getDwlxdh, correctionObjectInformationParam.getDwlxdh());
            }
            // 根据个人联系电话 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGrlxdh())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGrlxdh, correctionObjectInformationParam.getGrlxdh());
            }
            // 根据国籍 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGj())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGj, correctionObjectInformationParam.getGj());
            }
            // 根据国籍中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGjName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGjName, correctionObjectInformationParam.getGjName());
            }
            // 根据有无家庭成员及主要社会关系 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getYxjtcyjzyshgx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getYxjtcyjzyshgx, correctionObjectInformationParam.getYxjtcyjzyshgx());
            }
            // 根据户籍地是否与居住地相同 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjdsfyjzdxt())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjdsfyjzdxt, correctionObjectInformationParam.getHjdsfyjzdxt());
            }
            // 根据固定居住地所在省（区、市） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdszs())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdszs, correctionObjectInformationParam.getGdjzdszs());
            }
            // 根据固定居住地所在省（区、市）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdszsName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdszsName, correctionObjectInformationParam.getGdjzdszsName());
            }
            // 根据固定居住地所在地（市、州） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdszds())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdszds, correctionObjectInformationParam.getGdjzdszds());
            }
            // 根据固定居住地所在地（市、州）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdszdsName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdszdsName, correctionObjectInformationParam.getGdjzdszdsName());
            }
            // 根据固定居住地所在县（市、区） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdszxq())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdszxq, correctionObjectInformationParam.getGdjzdszxq());
            }
            // 根据固定居住地所在县（市、区）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdszxqName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdszxqName, correctionObjectInformationParam.getGdjzdszxqName());
            }
            // 根据固定居住地（乡镇、街道） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzd())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzd, correctionObjectInformationParam.getGdjzd());
            }
            // 根据固定居住地（乡镇、街道）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdName, correctionObjectInformationParam.getGdjzdName());
            }
            // 根据固定居住地明细 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getGdjzdmx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getGdjzdmx, correctionObjectInformationParam.getGdjzdmx());
            }
            // 根据户籍所在省（区、市） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszs())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszs, correctionObjectInformationParam.getHjszs());
            }
            // 根据户籍所在省（区、市）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszsName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszsName, correctionObjectInformationParam.getHjszsName());
            }
            // 根据户籍所在地（市、州） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszds())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszds, correctionObjectInformationParam.getHjszds());
            }
            // 根据户籍所在地（市、州）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszdsName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszdsName, correctionObjectInformationParam.getHjszdsName());
            }
            // 根据户籍所在县（市、区） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszxq())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszxq, correctionObjectInformationParam.getHjszxq());
            }
            // 根据户籍所在县（市、区）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszxqName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszxqName, correctionObjectInformationParam.getHjszxqName());
            }
            // 根据户籍所在地（乡镇、街道） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszd())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszd, correctionObjectInformationParam.getHjszd());
            }
            // 根据户籍所在地（乡镇、街道）中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszdName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszdName, correctionObjectInformationParam.getHjszdName());
            }
            // 根据户籍所在地明细 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getHjszdmx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getHjszdmx, correctionObjectInformationParam.getHjszdmx());
            }
            // 根据是否三无人员 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfswry())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfswry, correctionObjectInformationParam.getSfswry());
            }
            // 根据矫正机构 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzjg())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJzjg, correctionObjectInformationParam.getJzjg());
            }
            // 根据矫正机构中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJzjgName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJzjgName, correctionObjectInformationParam.getJzjgName());
            }
            // 根据是否有前科 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfyqk())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfyqk, correctionObjectInformationParam.getSfyqk());
            }
            // 根据是否累犯 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSflf())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSflf, correctionObjectInformationParam.getSflf());
            }
            // 根据前科类型 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getQklx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getQklx, correctionObjectInformationParam.getQklx());
            }
            // 根据前科类型中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getQklxName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getQklxName, correctionObjectInformationParam.getQklxName());
            }
            // 根据主要犯罪事实 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getZyfzss())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getZyfzss, correctionObjectInformationParam.getZyfzss());
            }
            // 根据社区矫正期限 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzqx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSqjzqx, correctionObjectInformationParam.getSqjzqx());
            }
            // 根据社区矫正开始日期 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzksrq())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSqjzksrq, correctionObjectInformationParam.getSqjzksrq());
            }
            // 根据社区矫正结束日期 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzjsrq())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSqjzjsrq, correctionObjectInformationParam.getSqjzjsrq());
            }
            // 根据犯罪类型 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getFzlx())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getFzlx, correctionObjectInformationParam.getFzlx());
            }
            // 根据犯罪类型中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getFzlxName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getFzlxName, correctionObjectInformationParam.getFzlxName());
            }
            // 根据具体罪名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJtzm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJtzm, correctionObjectInformationParam.getJtzm());
            }
            // 根据具体罪名中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getJtzmName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getJtzmName, correctionObjectInformationParam.getJtzmName());
            }
            // 根据是否“五独” 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfwd())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfwd, correctionObjectInformationParam.getSfwd());
            }
            // 根据是否“五涉” 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfws())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfws, correctionObjectInformationParam.getSfws());
            }
            // 根据是否有“四史” 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfyss())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfyss, correctionObjectInformationParam.getSfyss());
            }
            // 根据是否被宣告禁止令 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfbxgjzl())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfbxgjzl, correctionObjectInformationParam.getSfbxgjzl());
            }
            // 根据社区矫正人员接收日期 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzryjsrq())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSqjzryjsrq, correctionObjectInformationParam.getSqjzryjsrq());
            }
            // 根据社区矫正人员接收方式 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzryjsfs())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSqjzryjsfs, correctionObjectInformationParam.getSqjzryjsfs());
            }
            // 根据社区矫正人员接收方式中文 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSqjzryjsfsName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSqjzryjsfsName, correctionObjectInformationParam.getSqjzryjsfsName());
            }
            // 根据报到情况 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getBdqk())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getBdqk, correctionObjectInformationParam.getBdqk());
            }
            // 根据报到情况中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getBdqkName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getBdqkName, correctionObjectInformationParam.getBdqkName());
            }
            // 根据未按时报到情况说明 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getWasbdqksm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getWasbdqksm, correctionObjectInformationParam.getWasbdqksm());
            }
            // 根据是否建立矫正小组 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfjljzxz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfjljzxz, correctionObjectInformationParam.getSfjljzxz());
            }
            // 根据是否采用电子定位管理 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getSfcydzdwgl())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getSfcydzdwgl, correctionObjectInformationParam.getSfcydzdwgl());
            }
            // 根据电子定位方式 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getDzdwfs())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getDzdwfs, correctionObjectInformationParam.getDzdwfs());
            }
            // 根据电子定位方式中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getDzdwfsName())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getDzdwfsName, correctionObjectInformationParam.getDzdwfsName());
            }
            // 根据定位号码 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getDwhm())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getDwhm, correctionObjectInformationParam.getDwhm());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(correctionObjectInformationParam.getBz())) {
                queryWrapper.lambda().eq
                        (CorrectionObjectInformation::getBz, correctionObjectInformationParam.getBz());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionObjectInformation> list(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = new CorrectionObjectInformation();
        BeanUtil.copyProperties(correctionObjectInformationParam, correctionObjectInformation);
        this.save(correctionObjectInformation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CorrectionObjectInformationParam> correctionObjectInformationParamList) {
        correctionObjectInformationParamList.forEach(correctionObjectInformationParam -> {
            this.removeById(correctionObjectInformationParam.getId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = this.queryCorrectionObjectInformation(correctionObjectInformationParam);
        BeanUtil.copyProperties(correctionObjectInformationParam, correctionObjectInformation);
        this.updateById(correctionObjectInformation);
    }

    @Override
    public CorrectionObjectInformation detail(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return this.queryCorrectionObjectInformation(correctionObjectInformationParam);
    }

    /**
     * 获取矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    private CorrectionObjectInformation queryCorrectionObjectInformation(CorrectionObjectInformationParam correctionObjectInformationParam) {
        CorrectionObjectInformation correctionObjectInformation = this.getById(correctionObjectInformationParam.getId());
        if (ObjectUtil.isNull(correctionObjectInformation)) {
            throw new ServiceException(CorrectionObjectInformationExceptionEnum.NOT_EXIST);
        }
        return correctionObjectInformation;
    }

    @Override
    public void export(CorrectionObjectInformationParam correctionObjectInformationParam) {
        List<CorrectionObjectInformation> list = this.list(correctionObjectInformationParam);
        PoiUtil.exportExcelWithStream("SnowyCorrectionObjectInformation.xls", CorrectionObjectInformation.class, list);
    }

    @Override
    public List<String> getCorrectionIds() {
        return correctionObjectInformationMapper.getCorrectionIds();
    }

    @Override
    public List<String> getCorrectionWuxinIds() {
        return correctionObjectInformationMapper.getCorrectionWuxinIds();
    }

    @Override
    public List<String> getCorrectionIdsByDepart() {
        return correctionObjectInformationMapper.getCorrectionIdsByDepart();
    }

    @Override
    public void updateCorrectionZt(String id, String zhuangtai) {
        this.baseMapper.updateCorrectionZt(id, zhuangtai);
    }

    @Override
    public List<CorrectionObjectInformation> getCorrectionIdAndSfzhs() {
        return this.baseMapper.getCorrectionIdAndSfzhs();
    }

    @Override
    public List<ScreenVo> personnelClassification(List<String> collect) {
        return this.baseMapper.personnelClassification(collect);
    }

    @Scheduled(cron = "0 1/5 * * * ?")
    public void updateCorrectionInfo() {
        if (StrUtil.isNotEmpty(System.getProperty("spring.profiles.active")) && System.getProperty("spring.profiles.active").equals("dev")) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("account", "admin");
        jsonObject.put("password", "Jskj@2022");
        HttpRequest tokenRequest = new HttpRequest("http://10.249.2.210:9001/api/openApi/getToken");
        tokenRequest.method(Method.POST);
        tokenRequest.body(jsonObject.toJSONString());
        String post = tokenRequest.execute().body();
        JSONObject parseObject = JSON.parseObject(post);
        Object token = parseObject.get("data");
        log.info("开始更新矫正对象！");
        // 这里会更新前一年的数据
        DateTime dateTime = DateUtil.offsetMonth(DateUtil.date(), -12);
        String format = DateUtil.format(dateTime, DatePattern.UTC_SIMPLE_PATTERN);
        HttpRequest request = new HttpRequest("http://10.249.2.210:9001/api/openApi/personInfo/all");
        request.header("Authorization", token.toString());
        Map<String, Object> map = new HashMap<>();
        map.put("jzjg", "DEPT0000000000000000000000910500");
        map.put("lastModifiedTime", format);
        request.form(map);
        request.method(Method.GET);
        String body = request.execute().body();
        Object data = JSON.parseObject(body).get("data");
        List<CorrectionObjectInformation> correctionObjectInformationList = JSON.parseArray(JSON.toJSONString(data), CorrectionObjectInformation.class);
        this.saveOrUpdateBatch(correctionObjectInformationList);
        log.info("矫正对象更新完毕！");
        log.info("开始更新解矫状态！");
        HttpRequest terminateRequest = new HttpRequest("http://10.249.2.210:9001/api/openApi/personInfo/terminate");
        terminateRequest.header("Authorization", token.toString());
        terminateRequest.form(map);
        terminateRequest.method(Method.GET);
        String terminateBody = terminateRequest.execute().body();
        Object terminateData = JSON.parseObject(terminateBody).get("data");
        List<CorrectionTerminate> list = JSON.parseArray(JSON.toJSONString(terminateData), CorrectionTerminate.class);
        for (CorrectionTerminate correctionTerminate : list) {
            this.update(new UpdateWrapper<CorrectionObjectInformation>().lambda().eq(CorrectionObjectInformation::getId, correctionTerminate.getId()).set(CorrectionObjectInformation::getZhuangtai, correctionTerminate.getZhuangtai()));
        }
        log.info("解矫状态更新完成！");
        log.info("开始删除矫正对象！");
        HttpRequest deleteRequest = new HttpRequest("http://10.249.2.210:9001/api/openApi/personInfo/delete");
        deleteRequest.header("Authorization", token.toString());
        deleteRequest.form(map);
        deleteRequest.method(Method.GET);
        String deleteBody = deleteRequest.execute().body();
        Object deleteData = JSON.parseObject(deleteBody).get("data");
        List<CorrectionObjectInformationDelete> deleteList = JSON.parseArray(JSON.toJSONString(deleteData), CorrectionObjectInformationDelete.class);
        for (CorrectionObjectInformationDelete correctionObjectInformationDelete : deleteList) {
            this.removeById(correctionObjectInformationDelete.getId());
        }
        log.info("矫正对象删除完成！");
    }
}
