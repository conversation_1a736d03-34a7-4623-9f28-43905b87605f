
package com.concise.modular.unify.unifyuser.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;

import com.concise.modular.unify.unifyuser.entity.UnifyUser;
import com.concise.modular.unify.unifyuser.enums.UnifyUserExceptionEnum;
import com.concise.modular.unify.unifyuser.mapper.UnifyUserMapper;
import com.concise.modular.unify.unifyuser.param.UnifyUserParam;
import com.concise.modular.unify.unifyuser.service.UnifyUserService;
import com.concise.sys.core.enums.AdminTypeEnum;
import com.concise.sys.core.enums.SexEnum;
import com.concise.sys.modular.emp.entity.SysEmp;
import com.concise.sys.modular.emp.service.SysEmpService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 浙政钉用户表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-04-14 14:13:15
 */
@Service
public class UnifyUserServiceImpl extends ServiceImpl<UnifyUserMapper, UnifyUser> implements UnifyUserService {
    @Autowired
    private SysUserService sysUserServices;
    @Autowired
    private SysEmpService sysEmpService;
    @Autowired
    private SysOrgService sysOrgService;

    @Override
    public PageResult<UnifyUser> page(UnifyUserParam unifyUserParam) {
        QueryWrapper<UnifyUser> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(unifyUserParam)) {

            // 根据登录账号 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getUserName())) {
                queryWrapper.lambda().eq(UnifyUser::getUserName, unifyUserParam.getUserName());
            }
            // 根据密码 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getPassword())) {
                queryWrapper.lambda().eq(UnifyUser::getPassword, unifyUserParam.getPassword());
            }
            // 根据用户的显示名称 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getDisplayName())) {
                queryWrapper.lambda().eq(UnifyUser::getDisplayName, unifyUserParam.getDisplayName());
            }
            // 根据电子邮件 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getEmail())) {
                queryWrapper.lambda().eq(UnifyUser::getEmail, unifyUserParam.getEmail());
            }
            // 根据电话 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getPhoneNumbers())) {
                queryWrapper.lambda().eq(UnifyUser::getPhoneNumbers, unifyUserParam.getPhoneNumbers());
            }
            // 根据为账户指定组织单位 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getBelongs())) {
                queryWrapper.lambda().eq(UnifyUser::getBelongs, unifyUserParam.getBelongs());
            }
            // 根据是否禁用账户，ture 禁用账户,false 启用账 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getLocked())) {
                queryWrapper.lambda().eq(UnifyUser::getLocked, unifyUserParam.getLocked());
            }
            // 根据扩展字段,attributes 为系统定义扩展字段 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getExtendfield())) {
                queryWrapper.lambda().eq(UnifyUser::getExtendfield, unifyUserParam.getExtendfield());
            }
            // 根据employee码 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getMoziDeptCode())) {
                queryWrapper.lambda().eq(UnifyUser::getMoziDeptCode, unifyUserParam.getMoziDeptCode());
            }
            // 根据json字符串，完整保存 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getJsonString())) {
                queryWrapper.lambda().eq(UnifyUser::getJsonString, unifyUserParam.getJsonString());
            }
            // 根据删除时间 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getDeleteTime())) {
                queryWrapper.lambda().eq(UnifyUser::getDeleteTime, unifyUserParam.getDeleteTime());
            }
            // 根据删除标记 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getDeleteFlag())) {
                queryWrapper.lambda().eq(UnifyUser::getDeleteFlag, unifyUserParam.getDeleteFlag());
            }
            // 根据矫正机构id 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getCorrectOrgId())) {
                queryWrapper.lambda().eq(UnifyUser::getCorrectOrgId, unifyUserParam.getCorrectOrgId());
            }
            // 根据矫正机构名称 查询
            if (ObjectUtil.isNotEmpty(unifyUserParam.getCorrectOrgName())) {
                queryWrapper.lambda().eq(UnifyUser::getCorrectOrgName, unifyUserParam.getCorrectOrgName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<UnifyUser> list(UnifyUserParam unifyUserParam) {
        return this.list();
    }

    @Override
    public void add(UnifyUserParam unifyUserParam) {
        UnifyUser unifyUser = new UnifyUser();
        BeanUtil.copyProperties(unifyUserParam, unifyUser);
        this.save(unifyUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<UnifyUserParam> unifyUserParamList) {
        unifyUserParamList.forEach(unifyUserParam -> {
            this.removeById(unifyUserParam.getId());
            this.removeById(unifyUserParam.getExternalId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(UnifyUserParam unifyUserParam) {
        UnifyUser unifyUser = this.queryUnifyUser(unifyUserParam);
        BeanUtil.copyProperties(unifyUserParam, unifyUser);
        this.updateById(unifyUser);
    }

    @Override
    public UnifyUser detail(UnifyUserParam unifyUserParam) {
        return this.queryUnifyUser(unifyUserParam);
    }

    /**
     * 获取浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    private UnifyUser queryUnifyUser(UnifyUserParam unifyUserParam) {
        UnifyUser unifyUser = this.getById(unifyUserParam.getId());
        if (ObjectUtil.isNull(unifyUser)) {
            throw new ServiceException(UnifyUserExceptionEnum.NOT_EXIST);
        }
        return unifyUser;
    }

    @Override
    public void export(UnifyUserParam unifyUserParam) {
        List<UnifyUser> list = this.list(unifyUserParam);
        PoiUtil.exportExcelWithStream("SnowyUnifyUser.xls", UnifyUser.class, list);
    }

    @Override
    public void updateUserDepartToSysEmp() {
        List<UnifyUser> unifyUserList = this.list();
        for (UnifyUser unifyUser : unifyUserList) {
            SysEmp sysEmp = new SysEmp();
            sysEmp.setId(unifyUser.getId());
            if (StringUtils.isNotBlank(unifyUser.getCorrectOrgId())) {
                sysEmp.setJobNum(unifyUser.getCorrectOrgId());
            }
            sysEmp.setOrgId(unifyUser.getBelongs());


        }
    }

    @Override
    public void updateUserToSystemUser() {
        List<UnifyUser> list = this.list();
        for (UnifyUser unifyUser : list) {
            SysUser sysUser = sysUserServices.getById(unifyUser.getId());
            if (ObjectUtil.isNull(sysUser)) {
                SysUser newSysUser = new SysUser();
                newSysUser.setId(unifyUser.getId());
                newSysUser.setAccount(unifyUser.getUserName());
                newSysUser.setPassword("8de94883045eeb0c28f775fc26cff227");
                newSysUser.setName(unifyUser.getDisplayName());
                newSysUser.setPhone(unifyUser.getPhoneNumbers());
                newSysUser.setAdminType(AdminTypeEnum.NONE.getCode());
                newSysUser.setSex(SexEnum.NONE.getCode());
                sysUserServices.save(newSysUser);
            }
            if (ObjectUtil.isNotNull(unifyUser.getCorrectOrgId())) {
                SysEmp sysEmp = new SysEmp();
                sysEmp.setId(unifyUser.getId());
                sysEmp.setJobNum(unifyUser.getBelongs());
                sysEmp.setOrgId(unifyUser.getCorrectOrgId());
                SysOrg sysOrg = sysOrgService.getById(unifyUser.getCorrectOrgId());
                if (ObjectUtil.isNotNull(sysOrg)) {
                    sysEmp.setOrgName(unifyUser.getCorrectOrgName());
                    sysEmpService.saveOrUpdate(sysEmp);
                }
            }

        }
    }

}
