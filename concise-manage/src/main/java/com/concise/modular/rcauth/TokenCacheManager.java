package com.concise.modular.rcauth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

/**
 * Token缓存管理器
 * 用于管理access_token和refresh_token的缓存
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@Component
@Slf4j
public class TokenCacheManager {
    
    private static final String ACCESS_TOKEN_PREFIX = "auth:access_token:";
    private static final String REFRESH_TOKEN_PREFIX = "auth:refresh_token:";
    private static final String USER_TOKEN_PREFIX = "auth:user_token:";
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 缓存access_token
     * @param userId 用户ID
     * @param accessToken access_token
     * @param expiresIn 过期时间（秒）
     */
    public void cacheAccessToken(String userId, String accessToken, long expiresIn) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(accessToken)) {
            log.warn("缓存access_token失败：用户ID或token为空");
            return;
        }
        
        try {
            String key = ACCESS_TOKEN_PREFIX + userId;
            redisTemplate.opsForValue().set(key, accessToken, expiresIn, TimeUnit.SECONDS);
            
            // 同时建立token到用户的反向映射
            String userKey = USER_TOKEN_PREFIX + accessToken;
            redisTemplate.opsForValue().set(userKey, userId, expiresIn, TimeUnit.SECONDS);
            
            log.debug("缓存access_token成功，用户ID: {}", userId);
        } catch (Exception e) {
            log.error("缓存access_token异常", e);
        }
    }
    
    /**
     * 缓存refresh_token
     * @param userId 用户ID
     * @param refreshToken refresh_token
     * @param expiresIn 过期时间（秒）
     */
    public void cacheRefreshToken(String userId, String refreshToken, long expiresIn) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(refreshToken)) {
            log.warn("缓存refresh_token失败：用户ID或token为空");
            return;
        }
        
        try {
            String key = REFRESH_TOKEN_PREFIX + userId;
            redisTemplate.opsForValue().set(key, refreshToken, expiresIn, TimeUnit.SECONDS);
            log.debug("缓存refresh_token成功，用户ID: {}", userId);
        } catch (Exception e) {
            log.error("缓存refresh_token异常", e);
        }
    }
    
    /**
     * 获取缓存的access_token
     * @param userId 用户ID
     * @return access_token
     */
    public String getAccessToken(String userId) {
        if (!StringUtils.hasText(userId)) {
            return null;
        }
        
        try {
            String key = ACCESS_TOKEN_PREFIX + userId;
            Object token = redisTemplate.opsForValue().get(key);
            return token != null ? token.toString() : null;
        } catch (Exception e) {
            log.error("获取access_token异常", e);
            return null;
        }
    }
    
    /**
     * 获取缓存的refresh_token
     * @param userId 用户ID
     * @return refresh_token
     */
    public String getRefreshToken(String userId) {
        if (!StringUtils.hasText(userId)) {
            return null;
        }
        
        try {
            String key = REFRESH_TOKEN_PREFIX + userId;
            Object token = redisTemplate.opsForValue().get(key);
            return token != null ? token.toString() : null;
        } catch (Exception e) {
            log.error("获取refresh_token异常", e);
            return null;
        }
    }
    
    /**
     * 根据access_token获取用户ID
     * @param accessToken access_token
     * @return 用户ID
     */
    public String getUserIdByToken(String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            return null;
        }
        
        try {
            String key = USER_TOKEN_PREFIX + accessToken;
            Object userId = redisTemplate.opsForValue().get(key);
            return userId != null ? userId.toString() : null;
        } catch (Exception e) {
            log.error("根据token获取用户ID异常", e);
            return null;
        }
    }
    
    /**
     * 清除用户的所有token缓存
     * @param userId 用户ID
     */
    public void clearUserTokens(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }
        
        try {
            // 先获取access_token，用于清除反向映射
            String accessToken = getAccessToken(userId);
            
            // 清除access_token缓存
            String accessKey = ACCESS_TOKEN_PREFIX + userId;
            redisTemplate.delete(accessKey);
            
            // 清除refresh_token缓存
            String refreshKey = REFRESH_TOKEN_PREFIX + userId;
            redisTemplate.delete(refreshKey);
            
            // 清除反向映射
            if (StringUtils.hasText(accessToken)) {
                String userKey = USER_TOKEN_PREFIX + accessToken;
                redisTemplate.delete(userKey);
            }
            
            log.debug("清除用户token缓存成功，用户ID: {}", userId);
        } catch (Exception e) {
            log.error("清除用户token缓存异常", e);
        }
    }
    
    /**
     * 清除指定的access_token缓存
     * @param accessToken access_token
     */
    public void clearAccessToken(String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            return;
        }
        
        try {
            // 先获取用户ID
            String userId = getUserIdByToken(accessToken);
            
            if (StringUtils.hasText(userId)) {
                // 清除用户的access_token缓存
                String accessKey = ACCESS_TOKEN_PREFIX + userId;
                redisTemplate.delete(accessKey);
            }
            
            // 清除反向映射
            String userKey = USER_TOKEN_PREFIX + accessToken;
            redisTemplate.delete(userKey);
            
            log.debug("清除access_token缓存成功");
        } catch (Exception e) {
            log.error("清除access_token缓存异常", e);
        }
    }
}
